import 'dart:io';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';

class PdfExportService {
  /// Save PDF file with improved user experience
  static Future<bool> savePdfFile({
    required BuildContext context,
    required List<int> pdfBytes,
    required String fileName,
    bool showSuccessMessage = true,
  }) async {
    try {
      String? outputFile;
      
      if (Platform.isAndroid || Platform.isIOS) {
        // For mobile platforms, save to Downloads folder
        outputFile = await _saveMobileFile(pdfBytes, fileName);
      } else {
        // For desktop platforms, show save dialog
        outputFile = await FilePicker.platform.saveFile(
          dialogTitle: 'حفظ ملف PDF',
          fileName: fileName,
          type: FileType.custom,
          allowedExtensions: ['pdf'],
        );
        
        final file = File(outputFile!);
        await file.writeAsBytes(pdfBytes);
            }

      if (outputFile != null && showSuccessMessage) {
        _showSuccessMessage(context, outputFile);
        return true;
      } else if (outputFile == null) {
        _showCancelMessage(context);
        return false;
      }
      
      return true;
    } catch (e) {
      _showErrorMessage(context, e.toString());
      return false;
    }
  }

  /// Save file on mobile platforms
  static Future<String?> _saveMobileFile(List<int> pdfBytes, String fileName) async {
    try {
      Directory? directory;
      
      if (Platform.isAndroid) {
        // Try to get Downloads directory
        directory = Directory('/storage/emulated/0/Download');
        if (!await directory.exists()) {
          // Fallback to external storage
          directory = await getExternalStorageDirectory();
        }
      } else if (Platform.isIOS) {
        // For iOS, use Documents directory
        directory = await getApplicationDocumentsDirectory();
      }
      
      if (directory != null) {
        final file = File('${directory.path}/$fileName');
        await file.writeAsBytes(pdfBytes);
        return file.path;
      }
      
      return null;
    } catch (e) {
      debugPrint('Error saving mobile file: $e');
      return null;
    }
  }

  /// Show success message with file path and open folder option
  static void _showSuccessMessage(BuildContext context, String filePath) {
    if (!context.mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تم حفظ الملف بنجاح!',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 4),
            Text(
              'المسار: $filePath',
              style: const TextStyle(fontSize: 12),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 5),
        action: Platform.isAndroid || Platform.isIOS 
          ? SnackBarAction(
              label: 'فتح',
              textColor: Colors.white,
              onPressed: () => _openFile(filePath),
            )
          : SnackBarAction(
              label: 'فتح المجلد',
              textColor: Colors.white,
              onPressed: () => _openFileLocation(filePath),
            ),
      ),
    );
  }

  /// Show cancellation message
  static void _showCancelMessage(BuildContext context) {
    if (!context.mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم إلغاء حفظ الملف'),
        backgroundColor: Colors.orange,
        duration: Duration(seconds: 2),
      ),
    );
  }

  /// Show error message
  static void _showErrorMessage(BuildContext context, String error) {
    if (!context.mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('خطأ في حفظ الملف: $error'),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// Open file location in file manager
  static Future<void> _openFileLocation(String filePath) async {
    try {
      final directory = File(filePath).parent.path;
      
      if (Platform.isWindows) {
        await Process.run('explorer', [directory]);
      } else if (Platform.isMacOS) {
        await Process.run('open', [directory]);
      } else if (Platform.isLinux) {
        await Process.run('xdg-open', [directory]);
      }
    } catch (e) {
      debugPrint('Error opening file location: $e');
    }
  }

  /// Open file directly (for mobile)
  static Future<void> _openFile(String filePath) async {
    try {
      if (Platform.isAndroid) {
        await Process.run('am', [
          'start',
          '-a', 'android.intent.action.VIEW',
          '-d', 'file://$filePath',
          '-t', 'application/pdf'
        ]);
      } else if (Platform.isIOS) {
        // iOS will handle this through the share sheet
        debugPrint('File saved to: $filePath');
      }
    } catch (e) {
      debugPrint('Error opening file: $e');
    }
  }

  /// Get default file name for PDF export
  static String getDefaultFileName(String prefix, {String? customerName}) {
    final now = DateTime.now();
    final dateStr = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}';
    
    if (customerName != null && customerName.isNotEmpty) {
      return '${prefix}_${customerName}_$dateStr.pdf';
    } else {
      return '${prefix}_$dateStr.pdf';
    }
  }

  /// Check if storage permission is granted (Android)
  static Future<bool> checkStoragePermission() async {
    if (!Platform.isAndroid) return true;
    
    // This would require permission_handler package
    // For now, we'll assume permission is granted
    return true;
  }

  /// Get common save locations info
  static Map<String, String> getSaveLocationInfo() {
    if (Platform.isWindows) {
      return {
        'title': 'مكان الحفظ',
        'description': 'سيتم فتح نافذة لاختيار مكان حفظ الملف',
        'defaultLocation': 'المجلد الذي تختاره',
      };
    } else if (Platform.isAndroid) {
      return {
        'title': 'مكان الحفظ',
        'description': 'سيتم حفظ الملف في مجلد التحميلات',
        'defaultLocation': '/storage/emulated/0/Download/',
      };
    } else if (Platform.isIOS) {
      return {
        'title': 'مكان الحفظ',
        'description': 'سيتم حفظ الملف في مجلد المستندات',
        'defaultLocation': 'Documents folder',
      };
    } else {
      return {
        'title': 'مكان الحفظ',
        'description': 'سيتم فتح نافذة لاختيار مكان حفظ الملف',
        'defaultLocation': 'المجلد الذي تختاره',
      };
    }
  }
}
