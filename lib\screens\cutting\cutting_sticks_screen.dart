import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';
import '../../models/order_item.dart';
import '../../services/unified_cutting_service.dart';
import 'cutting_optimization_screen.dart';

class CuttingSticksScreen extends StatefulWidget {
  final OrderItem orderItem;

  const CuttingSticksScreen({
    super.key,
    required this.orderItem,
  });

  @override
  State<CuttingSticksScreen> createState() => _CuttingSticksScreenState();
}

class _CuttingSticksScreenState extends State<CuttingSticksScreen> {
  // Controllers for input fields
  final TextEditingController _stickLengthController = TextEditingController();
  final TextEditingController _sawBladeThicknessController = TextEditingController();
  final TextEditingController _pieceSizeController = TextEditingController();
  final TextEditingController _quantityController = TextEditingController();
  final TextEditingController _typeController = TextEditingController();
  final TextEditingController _numberController = TextEditingController();

  // Focus nodes
  final FocusNode _pieceSizeFocusNode = FocusNode();
  final FocusNode _quantityFocusNode = FocusNode();
  final FocusNode _typeFocusNode = FocusNode();
  final FocusNode _numberFocusNode = FocusNode();

  // Database helper
  final UnifiedCuttingService _databaseHelper = UnifiedCuttingService();

  // List to store cutting measurements
  List<StickMeasurement> _measurements = [];

  // List to store database IDs for measurements (parallel to _measurements)
  List<int> _measurementIds = [];

  @override
  void initState() {
    super.initState();
    // Initialize with default values
    _stickLengthController.text = '600'; // Default stick length in cm
    _sawBladeThicknessController.text = '0.5'; // Default saw blade thickness in cm

    // Load saved measurements for this order item
    _loadSavedMeasurements();
  }

  Future<void> _loadSavedMeasurements() async {
    try {
      final savedMeasurements = await _databaseHelper.getCuttingMeasurements(widget.orderItem.id!);

      if (savedMeasurements.isNotEmpty) {
        // Load stick length and saw blade thickness from the first measurement
        final firstMeasurement = savedMeasurements.first;
        _stickLengthController.text = firstMeasurement['stick_length'].toString();

        // Check if saw blade thickness is old value (3.0 or more) and update to new default (0.5)
        final savedThickness = firstMeasurement['saw_blade_thickness'] ?? 0.5;
        if (savedThickness >= 3.0) {
          _sawBladeThicknessController.text = '0.5';
        } else {
          _sawBladeThicknessController.text = savedThickness.toString();
        }

        // Convert saved measurements to StickMeasurement objects and store IDs
        final measurements = <StickMeasurement>[];
        final measurementIds = <int>[];

        for (final map in savedMeasurements) {
          measurements.add(StickMeasurement(
            pieceSize: map['piece_size'] ?? '',
            quantity: map['quantity'] ?? 0,
            type: map['type'] ?? '-',
            number: map['number'] ?? '-',
          ));
          measurementIds.add(map['id'] ?? 0);
        }

        setState(() {
          _measurements = measurements;
          _measurementIds = measurementIds;
        });
      }
    } catch (e) {
      // Handle error silently or show user-friendly message if needed
      debugPrint('Error loading saved measurements: $e');
    }
  }

  @override
  void dispose() {
    _stickLengthController.dispose();
    _sawBladeThicknessController.dispose();
    _pieceSizeController.dispose();
    _quantityController.dispose();
    _typeController.dispose();
    _numberController.dispose();
    _pieceSizeFocusNode.dispose();
    _quantityFocusNode.dispose();
    _typeFocusNode.dispose();
    _numberFocusNode.dispose();
    super.dispose();
  }

  Future<void> _addMeasurement() async {
    // الحقول المطلوبة فقط: مقاس القطعة والعدد
    if (_pieceSizeController.text.isNotEmpty &&
        _quantityController.text.isNotEmpty) {

      final pieceSize = _pieceSizeController.text.trim();
      final quantity = int.tryParse(_quantityController.text);
      // الحقول الاختيارية: النوع والرقم (يمكن أن تكون فارغة)
      final type = _typeController.text.trim().isEmpty ? '-' : _typeController.text.trim();
      final number = _numberController.text.trim().isEmpty ? '-' : _numberController.text.trim();

      if (quantity != null && quantity > 0) {
        try {
          // Save to database
          final measurementData = {
            'order_item_id': widget.orderItem.id!,
            'piece_size': pieceSize,
            'quantity': quantity,
            'type': type,
            'number': number,
            'stick_length': double.tryParse(_stickLengthController.text) ?? 300.0,
            'saw_blade_thickness': double.tryParse(_sawBladeThicknessController.text) ?? 0.5,
            'created_at': DateTime.now().millisecondsSinceEpoch,
          };

          final insertedId = await _databaseHelper.insertCuttingMeasurement(measurementData);

          setState(() {
            _measurements.add(StickMeasurement(
              pieceSize: pieceSize,
              quantity: quantity,
              type: type,
              number: number,
            ));
            _measurementIds.add(insertedId);

            // Clear input fields
            _pieceSizeController.clear();
            _quantityController.clear();
            _typeController.clear();
            _numberController.clear();

            // Focus back to piece size field
            _pieceSizeFocusNode.requestFocus();
          });

          // Show success message
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم حفظ المقاس بنجاح'),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 1),
              ),
            );
          }
        } catch (e) {
          // Show error message
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('خطأ في حفظ المقاس: $e'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      }
    }
  }

  Future<void> _removeMeasurement(int index) async {
    if (index < 0 || index >= _measurements.length) return;

    try {
      // Delete from database if we have a valid ID
      if (index < _measurementIds.length && _measurementIds[index] > 0) {
        await _databaseHelper.deleteCuttingMeasurement(_measurementIds[index]);
      }

      setState(() {
        _measurements.removeAt(index);
        if (index < _measurementIds.length) {
          _measurementIds.removeAt(index);
        }
      });

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف المقاس بنجاح'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حذف المقاس: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _calculateCutting() {
    if (_measurements.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إضافة مقاسات أولاً'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // Navigate to cutting optimization screen
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CuttingOptimizationScreen(
          orderItem: widget.orderItem,
          measurements: _measurements.map((m) => {
            'piece_size': m.pieceSize,
            'quantity': m.quantity,
            'type': m.type,
            'number': m.number,
          }).toList(),
          stickLength: double.tryParse(_stickLengthController.text) ?? 300.0,
          sawBladeThickness: double.tryParse(_sawBladeThicknessController.text) ?? 0.5,
        ),
      ),
    );
  }



  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text('${localizations.cutting} - ${widget.orderItem.itemName}'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 2,
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.of(context).pop(),
              tooltip: 'رجوع',
            ),
            IconButton(
              icon: const Icon(Icons.home),
              onPressed: () {
                Navigator.pushNamedAndRemoveUntil(context, '/home', (route) => false);
              },
              tooltip: 'الصفحة الرئيسية',
            ),
          ],
        ),
        leadingWidth: 100,
      ),
      body: LayoutBuilder(
        builder: (context, constraints) {
          // تحديد ما إذا كانت الشاشة صغيرة (موبايل) أم كبيرة (ويندوز/متصفح)
          bool isMobile = constraints.maxWidth < 800;

          if (isMobile) {
            // تصميم عمودي للموبايل
            return _buildMobileLayout(localizations);
          } else {
            // تصميم أفقي للشاشات الكبيرة
            return _buildDesktopLayout(localizations);
          }
        },
      ),
      floatingActionButton: _measurements.isNotEmpty
          ? FloatingActionButton.extended(
              onPressed: _calculateCutting,
              icon: const Icon(Icons.content_cut),
              label: const Text('بدء التقطيع'),
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            )
          : null,
    );
  }

  Widget _buildMobileLayout(AppLocalizations localizations) {
    return ScrollConfiguration(
      behavior: ScrollConfiguration.of(context).copyWith(
        scrollbars: true,
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Input section only - no results section for mobile
            _buildInputSection(localizations),
            const SizedBox(height: 12),
            // Enhanced Measurements DataGrid
            if (_measurements.isNotEmpty) ...[
              const SizedBox(height: 12),
              // Header with improved styling
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                    colors: [
                      Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                      Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
                    ],
                  ),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.list_alt,
                      color: Theme.of(context).colorScheme.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'المقاسات المضافة',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                        fontSize: 16,
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${_measurements.length}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Enhanced responsive DataTable container
              LayoutBuilder(
                builder: (context, constraints) {
                  final isTablet = constraints.maxWidth > 600;
                  final isMobile = constraints.maxWidth < 480;

                  return Container(
                    constraints: BoxConstraints(
                      maxHeight: isTablet ? 400 : (isMobile ? 300 : 350),
                      minHeight: 200,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(
                        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                      ),
                      borderRadius: const BorderRadius.only(
                        bottomLeft: Radius.circular(12),
                        bottomRight: Radius.circular(12),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withValues(alpha: 0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: SingleChildScrollView(
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: ConstrainedBox(
                          constraints: BoxConstraints(
                            minWidth: constraints.maxWidth,
                          ),
                          child: DataTable(
                              columnSpacing: isMobile ? 6 : (isTablet ? 12 : 10),
                              dataRowMinHeight: isMobile ? 42 : (isTablet ? 50 : 46),
                              dataRowMaxHeight: isMobile ? 46 : (isTablet ? 54 : 50),
                              headingRowHeight: isMobile ? 38 : (isTablet ? 46 : 42),
                              headingRowColor: WidgetStateProperty.all(
                                Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                              ),
                              border: TableBorder.all(
                                color: Colors.grey[300]!,
                                width: 0.5,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              dividerThickness: 0.5,
                              columns: [
                                DataColumn(
                                  label: Container(
                                    width: isMobile ? 30 : 40,
                                    padding: const EdgeInsets.symmetric(vertical: 4),
                                    child: Text(
                                      '#',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: isMobile ? 11 : (isTablet ? 14 : 12),
                                        color: Theme.of(context).colorScheme.primary,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ),
                                DataColumn(
                                  label: Container(
                                    width: isMobile ? 80 : 100,
                                    padding: const EdgeInsets.symmetric(vertical: 4),
                                    child: Text(
                                      'المقاس',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: isMobile ? 11 : (isTablet ? 14 : 12),
                                        color: Theme.of(context).colorScheme.primary,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ),
                                DataColumn(
                                  label: Container(
                                    width: isMobile ? 50 : 70,
                                    padding: const EdgeInsets.symmetric(vertical: 4),
                                    child: Text(
                                      'العدد',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: isMobile ? 11 : (isTablet ? 14 : 12),
                                        color: Theme.of(context).colorScheme.primary,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ),
                                if (!isMobile) // Hide on very small screens
                                  DataColumn(
                                    label: Container(
                                      padding: const EdgeInsets.symmetric(vertical: 4),
                                      child: Text(
                                        'النوع',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: isMobile ? 11 : (isTablet ? 14 : 12),
                                          color: Theme.of(context).colorScheme.primary,
                                        ),
                                      ),
                                    ),
                                  ),
                                if (!isMobile) // Hide on very small screens
                                  DataColumn(
                                    label: Container(
                                      padding: const EdgeInsets.symmetric(vertical: 4),
                                      child: Text(
                                        'الرقم',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: isMobile ? 11 : (isTablet ? 14 : 12),
                                          color: Theme.of(context).colorScheme.primary,
                                        ),
                                      ),
                                    ),
                                  ),
                                DataColumn(
                                  label: Container(
                                    padding: const EdgeInsets.symmetric(vertical: 4),
                                    child: Text(
                                      'إجراءات',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: isMobile ? 11 : (isTablet ? 14 : 12),
                                        color: Theme.of(context).colorScheme.primary,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                              rows: List.generate(_measurements.length, (index) {
                                final measurement = _measurements[index];
                                final isEvenRow = index % 2 == 0;

                                return DataRow(
                                  color: WidgetStateProperty.all(
                                    isEvenRow ? Colors.grey[50] : Colors.white,
                                  ),
                                  cells: [
                                    DataCell(
                                      Container(
                                        width: isMobile ? 30 : 40,
                                        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 2),
                                        child: Text(
                                          '${index + 1}',
                                          style: TextStyle(
                                            fontSize: isMobile ? 12 : (isTablet ? 15 : 13),
                                            fontWeight: FontWeight.w500,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                      ),
                                    ),
                                    DataCell(
                                      GestureDetector(
                                        onTap: () => _editMeasurement(index, 'pieceSize'),
                                        child: Container(
                                          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 2),
                                          decoration: BoxDecoration(
                                            color: Colors.blue.withValues(alpha: 0.05),
                                            borderRadius: BorderRadius.circular(4),
                                          ),
                                          child: Text(
                                            measurement.pieceSize,
                                            style: TextStyle(
                                              decoration: TextDecoration.underline,
                                              color: Colors.blue[700],
                                              fontSize: isMobile ? 12 : (isTablet ? 15 : 13),
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    DataCell(
                                      GestureDetector(
                                        onTap: () => _editMeasurement(index, 'quantity'),
                                        child: Container(
                                          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 2),
                                          decoration: BoxDecoration(
                                            color: Colors.green.withValues(alpha: 0.05),
                                            borderRadius: BorderRadius.circular(4),
                                          ),
                                          child: Text(
                                            '${measurement.quantity}',
                                            style: TextStyle(
                                              decoration: TextDecoration.underline,
                                              color: Colors.green[700],
                                              fontSize: isMobile ? 12 : (isTablet ? 15 : 13),
                                              fontWeight: FontWeight.w600,
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                        ),
                                      ),
                                    ),
                                    if (!isMobile) // Show only on larger screens
                                      DataCell(
                                        GestureDetector(
                                          onTap: () => _editMeasurement(index, 'type'),
                                          child: Container(
                                            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 2),
                                            decoration: BoxDecoration(
                                              color: Colors.purple.withValues(alpha: 0.05),
                                              borderRadius: BorderRadius.circular(4),
                                            ),
                                            child: Text(
                                              measurement.type.isEmpty ? '-' : measurement.type,
                                              style: TextStyle(
                                                decoration: measurement.type.isNotEmpty ? TextDecoration.underline : null,
                                                color: measurement.type.isNotEmpty ? Colors.purple[700] : Colors.grey[600],
                                                fontSize: isMobile ? 12 : (isTablet ? 15 : 13),
                                                fontWeight: FontWeight.w500,
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ),
                                      ),
                                    if (!isMobile) // Show only on larger screens
                                      DataCell(
                                        GestureDetector(
                                          onTap: () => _editMeasurement(index, 'number'),
                                          child: Container(
                                            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 2),
                                            decoration: BoxDecoration(
                                              color: Colors.orange.withValues(alpha: 0.05),
                                              borderRadius: BorderRadius.circular(4),
                                            ),
                                            child: Text(
                                              measurement.number.isEmpty ? '-' : measurement.number,
                                              style: TextStyle(
                                                decoration: measurement.number.isNotEmpty ? TextDecoration.underline : null,
                                                color: measurement.number.isNotEmpty ? Colors.orange[700] : Colors.grey[600],
                                                fontSize: isMobile ? 12 : (isTablet ? 15 : 13),
                                                fontWeight: FontWeight.w500,
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ),
                                      ),
                                    DataCell(
                                      Container(
                                        padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 1),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Container(
                                              decoration: BoxDecoration(
                                                color: Colors.blue.withValues(alpha: 0.1),
                                                borderRadius: BorderRadius.circular(6),
                                              ),
                                              child: IconButton(
                                                icon: Icon(
                                                  Icons.edit,
                                                  color: Colors.blue[700],
                                                  size: isMobile ? 16 : (isTablet ? 20 : 18),
                                                ),
                                                onPressed: () => _showEditDialog(index),
                                                tooltip: 'تعديل',
                                                padding: EdgeInsets.all(isMobile ? 4 : 6),
                                                constraints: BoxConstraints(
                                                  minWidth: isMobile ? 28 : 32,
                                                  minHeight: isMobile ? 28 : 32,
                                                ),
                                              ),
                                            ),
                                            SizedBox(width: isMobile ? 4 : 6),
                                            Container(
                                              decoration: BoxDecoration(
                                                color: Colors.red.withValues(alpha: 0.1),
                                                borderRadius: BorderRadius.circular(6),
                                              ),
                                              child: IconButton(
                                                icon: Icon(
                                                  Icons.delete_outline,
                                                  color: Colors.red[700],
                                                  size: isMobile ? 16 : (isTablet ? 20 : 18),
                                                ),
                                                onPressed: () => _removeMeasurement(index),
                                                tooltip: 'حذف',
                                                padding: EdgeInsets.all(isMobile ? 4 : 6),
                                                constraints: BoxConstraints(
                                                  minWidth: isMobile ? 28 : 32,
                                                  minHeight: isMobile ? 28 : 32,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                );
                              }),
                            ),
                          ),
                        ),
                      ),
                    );
                },
              ),

              // Show detailed mobile cards for very small screens
              LayoutBuilder(
                builder: (context, constraints) {
                  final isMobile = constraints.maxWidth < 480;

                  if (isMobile && _measurements.isNotEmpty) {
                    return Column(
                      children: [
                        const SizedBox(height: 12),
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.blue[50],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.blue[200]!),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(Icons.info_outline, color: Colors.blue[700], size: 16),
                                  const SizedBox(width: 6),
                                  Text(
                                    'تفاصيل المقاسات',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: Colors.blue[800],
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              SizedBox(
                                height: 150,
                                child: ListView.builder(
                                  itemCount: _measurements.length,
                                  itemBuilder: (context, index) {
                                    return _buildMobileDetailsCard(_measurements[index], index);
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDesktopLayout(AppLocalizations localizations) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Left side - Input section
          Expanded(
            flex: 1,
            child: _buildInputSection(localizations),
          ),

          const SizedBox(width: 24),

          // Right side - Results section
          Expanded(
            flex: 1,
            child: _buildResultsSection(localizations),
          ),
        ],
      ),
    );
  }

  Widget _buildInputSection(AppLocalizations localizations) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header - Compact
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.straighten,
                    color: Theme.of(context).colorScheme.primary,
                    size: 16,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    'مقاسات التقطيع',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 12),

            // Stick length and saw blade thickness input - Compact
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: _buildCompactInputField(
                    label: 'طول العود (سم)',
                    controller: _stickLengthController,
                    hint: '600',
                    icon: Icons.height,
                    keyboardType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildCompactInputField(
                    label: 'سمك شفرة المنشار (سم)',
                    controller: _sawBladeThicknessController,
                    hint: '0.5',
                    icon: Icons.content_cut_sharp,
                    keyboardType: TextInputType.number,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Measurements input section - Compact
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'إضافة مقاس جديد',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      fontSize: 13,
                    ),
                  ),

                  const SizedBox(height: 10),

                  // Row 1: مقاس القطعة و العدد
                  Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: _buildCompactInputField(
                          label: 'مقاس القطعة',
                          controller: _pieceSizeController,
                          focusNode: _pieceSizeFocusNode,
                          hint: 'مثال: 25 سم',
                          icon: Icons.straighten,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildCompactInputField(
                          label: 'العدد',
                          controller: _quantityController,
                          focusNode: _quantityFocusNode,
                          hint: '#',
                          icon: Icons.numbers,
                          keyboardType: TextInputType.number,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 10),

                  // Row 2: النوع و الرقم و زر الإضافة
                  Row(
                    children: [
                      Expanded(
                        child: _buildCompactInputField(
                          label: 'النوع (اختياري)',
                          controller: _typeController,
                          focusNode: _typeFocusNode,
                          hint: 'مثال: عارضة',
                          icon: Icons.category,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildCompactInputField(
                          label: 'الرقم (اختياري)',
                          controller: _numberController,
                          focusNode: _numberFocusNode,
                          hint: 'مثال: 1',
                          icon: Icons.tag,
                        ),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton.icon(
                        onPressed: _addMeasurement,
                        icon: const Icon(Icons.add, size: 16),
                        label: const Text('إضافة', style: TextStyle(fontSize: 12)),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).colorScheme.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
                          minimumSize: const Size(0, 32),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 12),

            // Enhanced Measurements DataGrid
            if (_measurements.isNotEmpty) ...[
              const SizedBox(height: 12),

              // Header with improved styling
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                    colors: [
                      Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                      Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
                    ],
                  ),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.list_alt,
                      color: Theme.of(context).colorScheme.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'المقاسات المضافة',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                        fontSize: 16,
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${_measurements.length}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Enhanced responsive DataTable container
              LayoutBuilder(
                builder: (context, constraints) {
                  final isTablet = constraints.maxWidth > 600;
                  final isMobile = constraints.maxWidth < 480;

                  return Container(
                    constraints: BoxConstraints(
                      maxHeight: isTablet ? 400 : (isMobile ? 300 : 350),
                      minHeight: 200,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(
                        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                      ),
                      borderRadius: const BorderRadius.only(
                        bottomLeft: Radius.circular(12),
                        bottomRight: Radius.circular(12),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withValues(alpha: 0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: SingleChildScrollView(
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: ConstrainedBox(
                          constraints: BoxConstraints(
                            minWidth: constraints.maxWidth,
                          ),
                          child: DataTable(
                              columnSpacing: isMobile ? 6 : (isTablet ? 12 : 10),
                              dataRowMinHeight: isMobile ? 42 : (isTablet ? 50 : 46),
                              dataRowMaxHeight: isMobile ? 46 : (isTablet ? 54 : 50),
                              headingRowHeight: isMobile ? 38 : (isTablet ? 46 : 42),
                              headingRowColor: WidgetStateProperty.all(
                                Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                              ),
                              border: TableBorder.all(
                                color: Colors.grey[300]!,
                                width: 0.5,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              dividerThickness: 0.5,
                              columns: [
                                DataColumn(
                                  label: Container(
                                    width: isMobile ? 30 : 40,
                                    padding: const EdgeInsets.symmetric(vertical: 4),
                                    child: Text(
                                      '#',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: isMobile ? 11 : (isTablet ? 14 : 12),
                                        color: Theme.of(context).colorScheme.primary,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ),
                                DataColumn(
                                  label: Container(
                                    width: isMobile ? 80 : 100,
                                    padding: const EdgeInsets.symmetric(vertical: 4),
                                    child: Text(
                                      'المقاس',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: isMobile ? 11 : (isTablet ? 14 : 12),
                                        color: Theme.of(context).colorScheme.primary,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ),
                                DataColumn(
                                  label: Container(
                                    width: isMobile ? 50 : 70,
                                    padding: const EdgeInsets.symmetric(vertical: 4),
                                    child: Text(
                                      'العدد',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: isMobile ? 11 : (isTablet ? 14 : 12),
                                        color: Theme.of(context).colorScheme.primary,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ),
                                if (!isMobile) // Hide on very small screens
                                  DataColumn(
                                    label: Container(
                                      padding: const EdgeInsets.symmetric(vertical: 4),
                                      child: Text(
                                        'النوع',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: isMobile ? 11 : (isTablet ? 14 : 12),
                                          color: Theme.of(context).colorScheme.primary,
                                        ),
                                      ),
                                    ),
                                  ),
                                if (!isMobile) // Hide on very small screens
                                  DataColumn(
                                    label: Container(
                                      padding: const EdgeInsets.symmetric(vertical: 4),
                                      child: Text(
                                        'الرقم',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: isMobile ? 11 : (isTablet ? 14 : 12),
                                          color: Theme.of(context).colorScheme.primary,
                                        ),
                                      ),
                                    ),
                                  ),
                                DataColumn(
                                  label: Container(
                                    padding: const EdgeInsets.symmetric(vertical: 4),
                                    child: Text(
                                      'إجراءات',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: isMobile ? 11 : (isTablet ? 14 : 12),
                                        color: Theme.of(context).colorScheme.primary,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                              rows: List.generate(_measurements.length, (index) {
                                final measurement = _measurements[index];
                                final isEvenRow = index % 2 == 0;

                                return DataRow(
                                  color: WidgetStateProperty.all(
                                    isEvenRow ? Colors.grey[50] : Colors.white,
                                  ),
                                  cells: [
                                    DataCell(
                                      Container(
                                        width: isMobile ? 30 : 40,
                                        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 2),
                                        child: Text(
                                          '${index + 1}',
                                          style: TextStyle(
                                            fontSize: isMobile ? 12 : (isTablet ? 15 : 13),
                                            fontWeight: FontWeight.w500,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                      ),
                                    ),
                                    DataCell(
                                      GestureDetector(
                                        onTap: () => _editMeasurement(index, 'pieceSize'),
                                        child: Container(
                                          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 2),
                                          decoration: BoxDecoration(
                                            color: Colors.blue.withValues(alpha: 0.05),
                                            borderRadius: BorderRadius.circular(4),
                                          ),
                                          child: Text(
                                            measurement.pieceSize,
                                            style: TextStyle(
                                              decoration: TextDecoration.underline,
                                              color: Colors.blue[700],
                                              fontSize: isMobile ? 12 : (isTablet ? 15 : 13),
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    DataCell(
                                      GestureDetector(
                                        onTap: () => _editMeasurement(index, 'quantity'),
                                        child: Container(
                                          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 2),
                                          decoration: BoxDecoration(
                                            color: Colors.green.withValues(alpha: 0.05),
                                            borderRadius: BorderRadius.circular(4),
                                          ),
                                          child: Text(
                                            '${measurement.quantity}',
                                            style: TextStyle(
                                              decoration: TextDecoration.underline,
                                              color: Colors.green[700],
                                              fontSize: isMobile ? 12 : (isTablet ? 15 : 13),
                                              fontWeight: FontWeight.w600,
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                        ),
                                      ),
                                    ),
                                    if (!isMobile) // Show only on larger screens
                                      DataCell(
                                        GestureDetector(
                                          onTap: () => _editMeasurement(index, 'type'),
                                          child: Container(
                                            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 2),
                                            decoration: BoxDecoration(
                                              color: Colors.purple.withValues(alpha: 0.05),
                                              borderRadius: BorderRadius.circular(4),
                                            ),
                                            child: Text(
                                              measurement.type.isEmpty ? '-' : measurement.type,
                                              style: TextStyle(
                                                decoration: measurement.type.isNotEmpty ? TextDecoration.underline : null,
                                                color: measurement.type.isNotEmpty ? Colors.purple[700] : Colors.grey[600],
                                                fontSize: isMobile ? 12 : (isTablet ? 15 : 13),
                                                fontWeight: FontWeight.w500,
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ),
                                      ),
                                    if (!isMobile) // Show only on larger screens
                                      DataCell(
                                        GestureDetector(
                                          onTap: () => _editMeasurement(index, 'number'),
                                          child: Container(
                                            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 2),
                                            decoration: BoxDecoration(
                                              color: Colors.orange.withValues(alpha: 0.05),
                                              borderRadius: BorderRadius.circular(4),
                                            ),
                                            child: Text(
                                              measurement.number.isEmpty ? '-' : measurement.number,
                                              style: TextStyle(
                                                decoration: measurement.number.isNotEmpty ? TextDecoration.underline : null,
                                                color: measurement.number.isNotEmpty ? Colors.orange[700] : Colors.grey[600],
                                                fontSize: isMobile ? 12 : (isTablet ? 15 : 13),
                                                fontWeight: FontWeight.w500,
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ),
                                      ),
                                    DataCell(
                                      Container(
                                        padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 1),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Container(
                                              decoration: BoxDecoration(
                                                color: Colors.blue.withValues(alpha: 0.1),
                                                borderRadius: BorderRadius.circular(6),
                                              ),
                                              child: IconButton(
                                                icon: Icon(
                                                  Icons.edit,
                                                  color: Colors.blue[700],
                                                  size: isMobile ? 16 : (isTablet ? 20 : 18),
                                                ),
                                                onPressed: () => _showEditDialog(index),
                                                tooltip: 'تعديل',
                                                padding: EdgeInsets.all(isMobile ? 4 : 6),
                                                constraints: BoxConstraints(
                                                  minWidth: isMobile ? 28 : 32,
                                                  minHeight: isMobile ? 28 : 32,
                                                ),
                                              ),
                                            ),
                                            SizedBox(width: isMobile ? 4 : 6),
                                            Container(
                                              decoration: BoxDecoration(
                                                color: Colors.red.withValues(alpha: 0.1),
                                                borderRadius: BorderRadius.circular(6),
                                              ),
                                              child: IconButton(
                                                icon: Icon(
                                                  Icons.delete_outline,
                                                  color: Colors.red[700],
                                                  size: isMobile ? 16 : (isTablet ? 20 : 18),
                                                ),
                                                onPressed: () => _removeMeasurement(index),
                                                tooltip: 'حذف',
                                                padding: EdgeInsets.all(isMobile ? 4 : 6),
                                                constraints: BoxConstraints(
                                                  minWidth: isMobile ? 28 : 32,
                                                  minHeight: isMobile ? 28 : 32,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                );
                              }),
                            ),
                          ),
                        ),
                      ),
                    );
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildResultsSection(AppLocalizations localizations) {
    // حساب الإحصائيات
    final totalPieces = _measurements.fold<int>(0, (sum, measurement) => sum + measurement.quantity);
    final uniqueSizes = _measurements.map((m) => m.pieceSize).toSet().length;
    final stickLength = double.tryParse(_stickLengthController.text) ?? 600.0;
    final sawThickness = double.tryParse(_sawBladeThicknessController.text) ?? 0.5;

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.analytics,
                    color: Theme.of(context).colorScheme.primary,
                    size: 16,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    'ملخص المقاسات',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            if (_measurements.isEmpty)
              // رسالة عدم وجود مقاسات
              Container(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    Icon(
                      Icons.info_outline,
                      size: 48,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'لم يتم إضافة أي مقاسات بعد',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'أضف المقاسات المطلوبة ثم اضغط "بدء التقطيع"',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[500],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              )
            else
              // إحصائيات المقاسات
              Column(
                children: [
                  // الصف الأول من الإحصائيات
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatCard(
                          'إجمالي القطع',
                          '$totalPieces',
                          Icons.straighten,
                          Colors.blue,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildStatCard(
                          'أنواع المقاسات',
                          '$uniqueSizes',
                          Icons.category,
                          Colors.green,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // الصف الثاني من الإحصائيات
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatCard(
                          'طول العود',
                          stickLength.toStringAsFixed(0),
                          Icons.height,
                          Colors.orange,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildStatCard(
                          'سمك المنشار',
                          sawThickness.toStringAsFixed(1),
                          Icons.content_cut,
                          Colors.red,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // رسالة تشجيعية
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.green[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.green[200]!),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.check_circle_outline,
                          color: Colors.green[600],
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'جاهز للتقطيع! اضغط "بدء التقطيع" لحساب التوزيع الأمثل',
                            style: TextStyle(
                              color: Colors.green[700],
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 20,
          ),
          const SizedBox(height: 6),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            title,
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCompactInputField({
    required String label,
    required TextEditingController controller,
    required String hint,
    required IconData icon,
    FocusNode? focusNode,
    TextInputType keyboardType = TextInputType.text,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 11,
          ),
        ),
        const SizedBox(height: 4),
        SizedBox(
          height: 36,
          child: TextField(
            controller: controller,
            focusNode: focusNode,
            keyboardType: keyboardType,
            style: const TextStyle(fontSize: 13),
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: const TextStyle(fontSize: 12),
              prefixIcon: Icon(icon, size: 16),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6),
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              isDense: true,
            ),
          ),
        ),
      ],
    );
  }

  // Edit measurement inline
  void _editMeasurement(int index, String field) {
    final measurement = _measurements[index];
    String currentValue = '';

    switch (field) {
      case 'pieceSize':
        currentValue = measurement.pieceSize;
        break;
      case 'quantity':
        currentValue = measurement.quantity.toString();
        break;
      case 'type':
        currentValue = measurement.type;
        break;
      case 'number':
        currentValue = measurement.number;
        break;
    }

    _showQuickEditDialog(index, field, currentValue);
  }

  // Show edit dialog for complete editing
  void _showEditDialog(int index) {
    final measurement = _measurements[index];

    final pieceSizeController = TextEditingController(text: measurement.pieceSize);
    final quantityController = TextEditingController(text: measurement.quantity.toString());
    final typeController = TextEditingController(text: measurement.type == '-' ? '' : measurement.type);
    final numberController = TextEditingController(text: measurement.number == '-' ? '' : measurement.number);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تعديل المقاس'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: pieceSizeController,
                decoration: const InputDecoration(
                  labelText: 'مقاس القطعة',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: quantityController,
                decoration: const InputDecoration(
                  labelText: 'العدد',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: typeController,
                decoration: const InputDecoration(
                  labelText: 'النوع (اختياري)',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: numberController,
                decoration: const InputDecoration(
                  labelText: 'الرقم (اختياري)',
                  border: OutlineInputBorder(),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (pieceSizeController.text.isNotEmpty && quantityController.text.isNotEmpty) {
                final quantity = int.tryParse(quantityController.text) ?? 1;

                _updateMeasurement(index, StickMeasurement(
                  pieceSize: pieceSizeController.text,
                  quantity: quantity,
                  type: typeController.text.isEmpty ? '-' : typeController.text,
                  number: numberController.text.isEmpty ? '-' : numberController.text,
                ));

                Navigator.pop(context);

                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم تحديث المقاس بنجاح'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  // Show quick edit dialog for single field
  void _showQuickEditDialog(int index, String field, String currentValue) {
    final controller = TextEditingController(text: currentValue == '-' ? '' : currentValue);
    String fieldLabel = '';
    TextInputType keyboardType = TextInputType.text;

    switch (field) {
      case 'pieceSize':
        fieldLabel = 'مقاس القطعة';
        keyboardType = TextInputType.number;
        break;
      case 'quantity':
        fieldLabel = 'العدد';
        keyboardType = TextInputType.number;
        break;
      case 'type':
        fieldLabel = 'النوع';
        break;
      case 'number':
        fieldLabel = 'الرقم';
        break;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تعديل $fieldLabel'),
        content: TextField(
          controller: controller,
          decoration: InputDecoration(
            labelText: fieldLabel,
            border: const OutlineInputBorder(),
          ),
          keyboardType: keyboardType,
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              final newValue = controller.text;
              if (newValue.isNotEmpty || (field == 'type' || field == 'number')) {
                _updateMeasurementField(index, field, newValue);
                Navigator.pop(context);

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('تم تحديث $fieldLabel بنجاح'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  // Update specific field of measurement
  void _updateMeasurementField(int index, String field, String newValue) {
    final measurement = _measurements[index];

    StickMeasurement updatedMeasurement;
    switch (field) {
      case 'pieceSize':
        updatedMeasurement = StickMeasurement(
          pieceSize: newValue,
          quantity: measurement.quantity,
          type: measurement.type,
          number: measurement.number,
        );
        break;
      case 'quantity':
        final quantity = int.tryParse(newValue) ?? measurement.quantity;
        updatedMeasurement = StickMeasurement(
          pieceSize: measurement.pieceSize,
          quantity: quantity,
          type: measurement.type,
          number: measurement.number,
        );
        break;
      case 'type':
        updatedMeasurement = StickMeasurement(
          pieceSize: measurement.pieceSize,
          quantity: measurement.quantity,
          type: newValue.isEmpty ? '-' : newValue,
          number: measurement.number,
        );
        break;
      case 'number':
        updatedMeasurement = StickMeasurement(
          pieceSize: measurement.pieceSize,
          quantity: measurement.quantity,
          type: measurement.type,
          number: newValue.isEmpty ? '-' : newValue,
        );
        break;
      default:
        return;
    }

    _updateMeasurement(index, updatedMeasurement);
  }

  // Update measurement in database and UI
  void _updateMeasurement(int index, StickMeasurement newMeasurement) async {
    try {
      // Update in database if measurement has an ID
      if (index < _measurementIds.length) {
        await _databaseHelper.updateCuttingMeasurement(
          _measurementIds[index],
          {
            'piece_size': newMeasurement.pieceSize,
            'quantity': newMeasurement.quantity,
            'type': newMeasurement.type,
            'number': newMeasurement.number,
          },
        );
      }

      // Update in UI
      setState(() {
        _measurements[index] = newMeasurement;
      });
    } catch (e) {
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث المقاس: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Build detailed card for mobile view
  Widget _buildMobileDetailsCard(StickMeasurement measurement, int index) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  '${index + 1}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 10,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  '${measurement.pieceSize} سم',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.green[100],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'عدد: ${measurement.quantity}',
                  style: TextStyle(
                    color: Colors.green[800],
                    fontWeight: FontWeight.w600,
                    fontSize: 11,
                  ),
                ),
              ),
            ],
          ),
          if (measurement.type.isNotEmpty || measurement.number.isNotEmpty) ...[
            const SizedBox(height: 6),
            Row(
              children: [
                if (measurement.type.isNotEmpty) ...[
                  Icon(Icons.category, size: 12, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    measurement.type,
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.grey[700],
                    ),
                  ),
                ],
                if (measurement.type.isNotEmpty && measurement.number.isNotEmpty)
                  const SizedBox(width: 12),
                if (measurement.number.isNotEmpty) ...[
                  Icon(Icons.tag, size: 12, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    'رقم: ${measurement.number}',
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.grey[700],
                    ),
                  ),
                ],
              ],
            ),
          ],
        ],
      ),
    );
  }
}

// Data models
class StickMeasurement {
  final String pieceSize;
  final int quantity;
  final String type;
  final String number;

  StickMeasurement({
    required this.pieceSize,
    required this.quantity,
    required this.type,
    required this.number,
  });
}
